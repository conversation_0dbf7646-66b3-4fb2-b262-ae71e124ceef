# Ubuntu 22.04 Docker-in-Docker Rootless 镜像 (x86_64)
# 使用多阶段构建优化镜像大小和构建效率

# ============================================================================
# 阶段1: 下载和准备Docker二进制文件
# ============================================================================
FROM ubuntu:22.04 AS downloader

ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 安装下载工具
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# 下载Docker完整包
RUN wget -O docker.tgz 'https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'

# 下载Docker Buildx
RUN wget -O docker-buildx 'https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-amd64' && \
    echo 'c41ed17ec05b6ebb50eeb02fb26cce90f16cd260b8d26ce73963428c6b2d6508 *docker-buildx' | sha256sum -c -

# 下载Docker Compose
RUN wget -O docker-compose 'https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-x86_64' && \
    echo '9040bd35b2cc0783ce6c5de491de7e52e24d4137dbfc5de8a524f718fc23556c *docker-compose' | sha256sum -c -

# 下载Rootless extras
RUN wget -O rootless.tgz 'https://download.docker.com/linux/static/stable/x86_64/docker-rootless-extras-28.2.1.tgz'

# 下载dind脚本
ENV DIND_COMMIT=8d9e3502aba39127e4d12196dae16d306f76993d
RUN wget -O dind "https://raw.githubusercontent.com/docker/docker/${DIND_COMMIT}/hack/dind"

# ============================================================================
# 阶段2: 构建最终的Rootless DIND镜像
# ============================================================================
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    # Docker运行时依赖
    btrfs-progs \
    e2fsprogs \
    git \
    iptables \
    openssl \
    pigz \
    uidmap \
    xfsprogs \
    xz-utils \
    zfsutils-linux \
    # 网络和系统工具
    iproute2 \
    kmod \
    ca-certificates \
    # Rootless特定依赖
    fuse-overlayfs \
    && rm -rf /var/lib/apt/lists/*

# 设置iptables legacy支持
RUN update-alternatives --set iptables /usr/sbin/iptables-legacy && \
    update-alternatives --set ip6tables /usr/sbin/ip6tables-legacy && \
    update-alternatives --set arptables /usr/sbin/arptables-legacy && \
    update-alternatives --set ebtables /usr/sbin/ebtables-legacy

# 创建运行时目录
RUN mkdir /run/user && chmod 1777 /run/user

# 创建rootless用户
RUN adduser --home /home/<USER>'Rootless' --disabled-password --uid 1000 rootless && \
    echo 'rootless:100000:65536' >> /etc/subuid && \
    echo 'rootless:100000:65536' >> /etc/subgid

# 设置dockremap用户（用于userns-remap）
RUN addgroup --system dockremap && \
    adduser --system --ingroup dockremap dockremap && \
    echo 'dockremap:165536:65536' >> /etc/subuid && \
    echo 'dockremap:165536:65536' >> /etc/subgid

# 从下载阶段复制并安装Docker二进制文件
COPY --from=downloader /docker.tgz /tmp/
RUN tar --extract \
        --file /tmp/docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner && \
    rm /tmp/docker.tgz

# 安装Docker Buildx插件
COPY --from=downloader /docker-buildx /tmp/
RUN mkdir -p /usr/local/libexec/docker/cli-plugins && \
    mv /tmp/docker-buildx /usr/local/libexec/docker/cli-plugins/docker-buildx && \
    chmod +x /usr/local/libexec/docker/cli-plugins/docker-buildx

# 安装Docker Compose插件
COPY --from=downloader /docker-compose /tmp/
RUN mv /tmp/docker-compose /usr/local/libexec/docker/cli-plugins/docker-compose && \
    chmod +x /usr/local/libexec/docker/cli-plugins/docker-compose

# 安装Rootless extras
COPY --from=downloader /rootless.tgz /tmp/
RUN tar --extract \
        --file /tmp/rootless.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        'docker-rootless-extras/rootlesskit' \
        'docker-rootless-extras/vpnkit' && \
    rm /tmp/rootless.tgz

# 安装dind脚本
COPY --from=downloader /dind /usr/local/bin/
RUN chmod +x /usr/local/bin/dind

# 复制入口脚本
COPY dockerd-entrypoint.sh /usr/local/bin/
COPY docker-entrypoint.sh /usr/local/bin/

# 为rootless用户创建Docker数据目录
RUN mkdir -p /home/<USER>/.local/share/docker && \
    chown -R rootless:rootless /home/<USER>/.local/share/docker

# 验证安装
RUN docker --version && \
    dockerd --version && \
    containerd --version && \
    ctr --version && \
    runc --version && \
    rootlesskit --version && \
    vpnkit --version && \
    docker buildx version && \
    docker compose version

# 设置TLS证书目录
ENV DOCKER_TLS_CERTDIR=/certs
RUN mkdir -p /certs /certs/client && chmod 1777 /certs /certs/client

# 设置卷
VOLUME /home/<USER>/.local/share/docker

# 暴露端口
EXPOSE 2375 2376

# 切换到rootless用户
USER rootless

# 设置入口点
ENTRYPOINT ["dockerd-entrypoint.sh"]
CMD []

# Ubuntu 22.04 Docker-in-Docker 镜像 (x86_64)

这是一套基于Ubuntu 22.04的定制Docker镜像，实现了Docker-in-Docker功能，默认支持Rootless模式，专为x86_64架构优化。

## 特性

- ✅ 基于Ubuntu 22.04 LTS（而非Alpine）
- ✅ 使用官方dind实现方式
- ✅ 默认Rootless模式，无需root权限
- ✅ 完整的Docker CLI、Buildx和Compose支持
- ✅ 专为x86_64架构优化

## 镜像变体

### 1. ubuntu-docker:28.2.1-cli
仅包含Docker CLI工具的基础镜像。

### 2. ubuntu-docker:28.2.1-dind
完整的Docker-in-Docker镜像，支持以root权限运行dockerd。

### 3. ubuntu-docker:28.2.1-dind-rootless（推荐）
Rootless Docker-in-Docker镜像，默认以非root用户运行，更安全。

## 快速开始

### 构建镜像

```bash
# 给构建脚本执行权限
chmod +x build-ubuntu-docker.sh

# 构建所有镜像
./build-ubuntu-docker.sh
```

### 使用Rootless模式（推荐）

```bash
# 启动rootless dind容器
docker run --privileged --name ubuntu-dind-rootless -d \
  ubuntu-docker:28.2.1-dind-rootless

# 验证Docker运行状态
docker exec ubuntu-dind-rootless docker version

# 运行测试容器
docker exec ubuntu-dind-rootless docker run hello-world

# 进入容器交互式shell
docker exec -it ubuntu-dind-rootless bash
```

### 使用传统DIND模式

```bash
# 启动dind容器
docker run --privileged --name ubuntu-dind -d \
  ubuntu-docker:28.2.1-dind

# 验证Docker运行状态
docker exec ubuntu-dind docker version
```

### 挂载卷以持久化数据

```bash
# Rootless模式
docker run --privileged --name ubuntu-dind-rootless \
  -v docker-data:/home/<USER>/.local/share/docker \
  -d ubuntu-docker:28.2.1-dind-rootless

# 传统模式
docker run --privileged --name ubuntu-dind \
  -v docker-data:/var/lib/docker \
  -d ubuntu-docker:28.2.1-dind
```

### 暴露Docker API

```bash
# 暴露Docker API端口（注意安全性）
docker run --privileged --name ubuntu-dind-rootless \
  -p 2376:2376 \
  -e DOCKER_TLS_CERTDIR=/certs \
  -v docker-certs:/certs/client \
  -d ubuntu-docker:28.2.1-dind-rootless

# 从主机连接到容器内的Docker
export DOCKER_HOST=tcp://localhost:2376
export DOCKER_TLS_VERIFY=1
export DOCKER_CERT_PATH=/path/to/certs/client
docker version
```

## 环境变量

### 通用环境变量

- `DOCKER_TLS_CERTDIR`: TLS证书目录（默认：`/certs`）
- `DOCKER_HOST`: Docker守护进程地址
- `DOCKER_TLS_VERIFY`: 启用TLS验证

### Rootless特定环境变量

- `DOCKERD_ROOTLESS_ROOTLESSKIT_NET`: 网络驱动（默认：`vpnkit`）
- `DOCKERD_ROOTLESS_ROOTLESSKIT_MTU`: MTU大小（默认：`1500`）
- `DOCKERD_ROOTLESS_ROOTLESSKIT_FLAGS`: 额外的rootlesskit标志

## 安全注意事项

1. **Rootless模式更安全**：推荐使用rootless变体，它以非特权用户运行Docker守护进程。

2. **--privileged标志**：虽然容器需要`--privileged`标志，但rootless模式下Docker守护进程本身不以root运行。

3. **网络隔离**：rootless模式提供更好的网络隔离。

4. **文件系统权限**：rootless模式下的文件系统操作受到用户命名空间的限制。

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保容器以--privileged模式运行
   docker run --privileged ...
   ```

2. **网络问题**
   ```bash
   # 检查rootlesskit网络配置
   docker exec container-name rootlesskit --help
   ```

3. **存储驱动问题**
   ```bash
   # 检查可用的存储驱动
   docker exec container-name docker info | grep "Storage Driver"
   ```

## 与官方镜像的区别

| 特性 | 官方Alpine镜像 | 本Ubuntu镜像 |
|------|----------------|--------------|
| 基础系统 | Alpine Linux | Ubuntu 22.04 LTS |
| 包管理器 | apk | apt |
| 系统库 | musl libc | glibc |
| 镜像大小 | 较小 | 较大但更兼容 |
| 默认模式 | Root | Rootless |
| 安全性 | 标准 | 增强（rootless） |

## 许可证

本项目基于Docker官方镜像，遵循相同的许可证条款。

version: '3.8'

services:
  # Ubuntu Docker-in-Docker Rootless (统一镜像)
  ubuntu-dind-rootless:
    build:
      context: .
      dockerfile: Dockerfile
    image: ubuntu-docker:28.2.1-dind-rootless
    container_name: ubuntu-dind-rootless
    privileged: true
    environment:
      - DOCKER_TLS_CERTDIR=/certs
      # Rootless特定配置
      - DOCKERD_ROOTLESS_ROOTLESSKIT_NET=vpnkit
      - DOCKERD_ROOTLESS_ROOTLESSKIT_MTU=1500
    volumes:
      # 持久化Docker数据（rootless模式）
      - ubuntu-docker-data:/home/<USER>/.local/share/docker
      # TLS证书
      - ubuntu-docker-certs:/certs/client:ro
    ports:
      - "2376:2376"  # Docker API (TLS)
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "docker", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 开发环境 - 带工作目录挂载
  ubuntu-dind-dev:
    build:
      context: .
      dockerfile: Dockerfile
    image: ubuntu-docker:latest
    container_name: ubuntu-dind-dev
    privileged: true
    environment:
      - DOCKER_TLS_CERTDIR=/certs
      - DOCKERD_ROOTLESS_ROOTLESSKIT_NET=vpnkit
      - DOCKERD_ROOTLESS_ROOTLESSKIT_MTU=1500
    volumes:
      # 持久化Docker数据
      - ubuntu-docker-dev-data:/home/<USER>/.local/share/docker
      # TLS证书
      - ubuntu-docker-certs:/certs/client:ro
      # 挂载当前目录用于开发
      - ./:/workspace
    working_dir: /workspace
    ports:
      - "2377:2376"  # 避免端口冲突
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "docker", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - dev  # 开发环境profile

volumes:
  # 主要的Docker数据存储
  ubuntu-docker-data:
    driver: local

  # 开发环境的Docker数据存储
  ubuntu-docker-dev-data:
    driver: local

  # TLS证书存储
  ubuntu-docker-certs:
    driver: local

networks:
  default:
    name: ubuntu-docker-network

version: '3.8'

services:
  # Rootless Docker-in-Docker (推荐)
  ubuntu-dind-rootless:
    build:
      context: .
      dockerfile: Dockerfile.ubuntu-dind-rootless
    image: ubuntu-docker:28.2.1-dind-rootless
    container_name: ubuntu-dind-rootless
    privileged: true
    environment:
      - DOCKER_TLS_CERTDIR=/certs
      # Rootless特定配置
      - DOCKERD_ROOTLESS_ROOTLESSKIT_NET=vpnkit
      - DOCKERD_ROOTLESS_ROOTLESSKIT_MTU=1500
    volumes:
      # 持久化Docker数据（rootless模式）
      - ubuntu-docker-rootless-data:/home/<USER>/.local/share/docker
      # TLS证书
      - ubuntu-docker-certs:/certs/client:ro
    ports:
      - "2376:2376"  # Docker API (TLS)
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "docker", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 传统Docker-in-Docker
  ubuntu-dind:
    build:
      context: .
      dockerfile: Dockerfile.ubuntu-dind
    image: ubuntu-docker:28.2.1-dind
    container_name: ubuntu-dind
    privileged: true
    environment:
      - DOCKER_TLS_CERTDIR=/certs
    volumes:
      # 持久化Docker数据（传统模式）
      - ubuntu-docker-data:/var/lib/docker
      # TLS证书
      - ubuntu-docker-certs:/certs/client:ro
    ports:
      - "2375:2375"  # Docker API (无TLS)
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "docker", "version"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    profiles:
      - traditional  # 使用profile来选择性启动

  # Docker CLI工具容器
  ubuntu-docker-cli:
    build:
      context: .
      dockerfile: Dockerfile.ubuntu-cli
    image: ubuntu-docker:28.2.1-cli
    container_name: ubuntu-docker-cli
    environment:
      # 连接到rootless dind
      - DOCKER_HOST=tcp://ubuntu-dind-rootless:2376
      - DOCKER_TLS_VERIFY=1
      - DOCKER_CERT_PATH=/certs/client
    volumes:
      - ubuntu-docker-certs:/certs/client:ro
      - ./:/workspace
    working_dir: /workspace
    depends_on:
      - ubuntu-dind-rootless
    profiles:
      - cli  # 使用profile来选择性启动

volumes:
  # Rootless模式的Docker数据
  ubuntu-docker-rootless-data:
    driver: local
  
  # 传统模式的Docker数据
  ubuntu-docker-data:
    driver: local
  
  # TLS证书存储
  ubuntu-docker-certs:
    driver: local

networks:
  default:
    name: ubuntu-docker-network

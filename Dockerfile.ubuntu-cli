# Docker CLI on Ubuntu 22.04
FROM ubuntu:22.04

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Docker CLI
RUN set -eux; \
    \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "$dpkgArch" in \
        'amd64') \
            url='https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'; \
            ;; \
        'arm64') \
            url='https://download.docker.com/linux/static/stable/aarch64/docker-28.2.1.tgz'; \
            ;; \
        'armhf') \
            url='https://download.docker.com/linux/static/stable/armhf/docker-28.2.1.tgz'; \
            ;; \
        *) echo >&2 "error: unsupported architecture ($dpkgArch)"; exit 1 ;; \
    esac; \
    \
    wget -O docker.tgz "$url"; \
    \
    tar --extract \
        --file docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner \
        'docker/docker' \
    ; \
    rm docker.tgz; \
    \
    docker --version

# 安装Docker Buildx
RUN set -eux; \
    \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "$dpkgArch" in \
        'amd64') \
            url='https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-amd64'; \
            sha256='c41ed17ec05b6ebb50eeb02fb26cce90f16cd260b8d26ce73963428c6b2d6508'; \
            ;; \
        'arm64') \
            url='https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-arm64'; \
            sha256='ad33819d085a635e3b4400a412bd2b4e943bfbc830366d78f50579bae48f8053'; \
            ;; \
        'armhf') \
            url='https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-arm-v7'; \
            sha256='69a3afa3d22867ea67b87e5f205574478e7a795599c471b61575bacf455452ae'; \
            ;; \
        *) echo >&2 "error: unsupported buildx architecture ($dpkgArch)"; exit 1 ;; \
    esac; \
    \
    wget -O docker-buildx "$url"; \
    echo "$sha256 *docker-buildx" | sha256sum -c -; \
    \
    plugin='/usr/local/libexec/docker/cli-plugins/docker-buildx'; \
    mkdir -p "$(dirname "$plugin")"; \
    mv docker-buildx "$plugin"; \
    chmod +x "$plugin"; \
    \
    docker buildx version

# 安装Docker Compose
RUN set -eux; \
    \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "$dpkgArch" in \
        'amd64') \
            url='https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-x86_64'; \
            sha256='9040bd35b2cc0783ce6c5de491de7e52e24d4137dbfc5de8a524f718fc23556c'; \
            ;; \
        'arm64') \
            url='https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-aarch64'; \
            sha256='d1148609319706a57b755ff0f61d604a63a8cf57adb24c17535baa766ff14b4f'; \
            ;; \
        'armhf') \
            url='https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-armv7'; \
            sha256='9e9d20ebc4a094ee7788fbb5bddf70b0b319a55eee134db195d1e47f078ae0dc'; \
            ;; \
        *) echo >&2 "error: unsupported compose architecture ($dpkgArch)"; exit 1 ;; \
    esac; \
    \
    wget -O docker-compose "$url"; \
    echo "$sha256 *docker-compose" | sha256sum -c -; \
    \
    plugin='/usr/local/libexec/docker/cli-plugins/docker-compose'; \
    mkdir -p "$(dirname "$plugin")"; \
    mv docker-compose "$plugin"; \
    chmod +x "$plugin"; \
    \
    docker compose version

# 复制docker-entrypoint.sh脚本
COPY docker-entrypoint.sh /usr/local/bin/

ENV DOCKER_TLS_CERTDIR=/certs
RUN mkdir /certs /certs/client && chmod 1777 /certs /certs/client

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["sh"]

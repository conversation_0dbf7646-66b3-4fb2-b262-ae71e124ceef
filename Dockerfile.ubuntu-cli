# Docker CLI on Ubuntu 22.04
FROM ubuntu:22.04

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Docker CLI (仅支持x86_64)
RUN set -eux; \
    wget -O docker.tgz 'https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'; \
    \
    tar --extract \
        --file docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner \
        'docker/docker' \
    ; \
    rm docker.tgz; \
    \
    docker --version

# 安装Docker Buildx (仅支持x86_64)
RUN set -eux; \
    wget -O docker-buildx 'https://github.com/docker/buildx/releases/download/v0.24.0/buildx-v0.24.0.linux-amd64'; \
    echo 'c41ed17ec05b6ebb50eeb02fb26cce90f16cd260b8d26ce73963428c6b2d6508 *docker-buildx' | sha256sum -c -; \
    \
    plugin='/usr/local/libexec/docker/cli-plugins/docker-buildx'; \
    mkdir -p "$(dirname "$plugin")"; \
    mv docker-buildx "$plugin"; \
    chmod +x "$plugin"; \
    \
    docker buildx version

# 安装Docker Compose (仅支持x86_64)
RUN set -eux; \
    wget -O docker-compose 'https://github.com/docker/compose/releases/download/v2.36.2/docker-compose-linux-x86_64'; \
    echo '9040bd35b2cc0783ce6c5de491de7e52e24d4137dbfc5de8a524f718fc23556c *docker-compose' | sha256sum -c -; \
    \
    plugin='/usr/local/libexec/docker/cli-plugins/docker-compose'; \
    mkdir -p "$(dirname "$plugin")"; \
    mv docker-compose "$plugin"; \
    chmod +x "$plugin"; \
    \
    docker compose version

# 复制docker-entrypoint.sh脚本
COPY docker-entrypoint.sh /usr/local/bin/

ENV DOCKER_TLS_CERTDIR=/certs
RUN mkdir /certs /certs/client && chmod 1777 /certs /certs/client

ENTRYPOINT ["docker-entrypoint.sh"]
CMD ["sh"]

#!/bin/bash

# 演示Rootless vs Root模式的区别

echo "🔍 Docker Rootless vs Root 模式对比演示"
echo "========================================"

# 检查是否有Docker容器运行
if ! docker ps > /dev/null 2>&1; then
    echo "❌ 请先启动Docker容器"
    echo "运行: docker run --privileged --name demo-dind -d ubuntu-docker:latest"
    exit 1
fi

CONTAINER_NAME="demo-dind"

echo ""
echo "1️⃣  用户身份对比"
echo "----------------"

echo "🔹 容器内Docker守护进程运行用户:"
docker exec $CONTAINER_NAME ps aux | grep dockerd | head -1

echo ""
echo "🔹 容器内当前用户身份:"
docker exec $CONTAINER_NAME id

echo ""
echo "🔹 容器内用户命名空间映射:"
docker exec $CONTAINER_NAME cat /proc/self/uid_map 2>/dev/null || echo "无用户命名空间映射"

echo ""
echo "2️⃣  文件系统权限对比"
echo "-------------------"

echo "🔹 尝试访问敏感目录:"
docker exec $CONTAINER_NAME ls -la /proc/1/ 2>/dev/null | head -3 || echo "访问受限"

echo ""
echo "🔹 Docker数据目录权限:"
docker exec $CONTAINER_NAME ls -la /home/<USER>/.local/share/docker/ | head -3

echo ""
echo "3️⃣  网络权限对比"
echo "---------------"

echo "🔹 尝试绑定特权端口(80):"
docker exec $CONTAINER_NAME timeout 2 nc -l 80 2>&1 || echo "无法绑定特权端口(正常现象)"

echo ""
echo "🔹 绑定非特权端口(8080):"
docker exec $CONTAINER_NAME timeout 2 nc -l 8080 2>&1 &
sleep 1
echo "可以绑定非特权端口"

echo ""
echo "4️⃣  容器运行测试"
echo "---------------"

echo "🔹 在Rootless Docker中运行容器:"
docker exec $CONTAINER_NAME docker run --rm alpine id

echo ""
echo "🔹 容器内文件创建权限:"
docker exec $CONTAINER_NAME docker run --rm -v /tmp:/host alpine touch /host/rootless-test 2>/dev/null && echo "文件创建成功" || echo "文件创建受限"

echo ""
echo "5️⃣  安全特性验证"
echo "---------------"

echo "🔹 检查用户命名空间:"
docker exec $CONTAINER_NAME docker run --rm alpine cat /proc/self/uid_map

echo ""
echo "🔹 检查capabilities:"
docker exec $CONTAINER_NAME docker run --rm alpine cat /proc/self/status | grep Cap

echo ""
echo "📋 总结"
echo "======"
echo "✅ Rootless模式特点:"
echo "   - Docker守护进程以普通用户运行"
echo "   - 容器内root映射为主机普通用户"
echo "   - 无法访问主机敏感资源"
echo "   - 无法绑定特权端口"
echo "   - 提供更好的安全隔离"
echo ""
echo "⚠️  传统Root模式风险:"
echo "   - 容器逃逸可获得主机root权限"
echo "   - 可访问主机所有资源"
echo "   - 安全风险较高"
echo ""
echo "🎯 推荐: 生产环境使用Rootless模式"

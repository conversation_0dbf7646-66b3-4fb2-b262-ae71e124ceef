#!/bin/bash
set -e

# 构建基于Ubuntu 22.04的Docker DIND Rootless镜像脚本 (x86_64)

DOCKER_VERSION="28.2.1"
TAG_PREFIX="ubuntu-docker"

echo "构建基于Ubuntu 22.04的Docker DIND Rootless镜像 (x86_64)..."

# 构建统一的DIND Rootless镜像（使用多阶段构建）
echo "构建Docker-in-Docker Rootless镜像（多阶段构建）..."
docker build -f Dockerfile -t "${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless" .

# 创建便捷的标签
docker tag "${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless" "${TAG_PREFIX}:latest"

echo "构建完成！"
echo ""
echo "可用的镜像："
echo "  ${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless - Docker-in-Docker Rootless (推荐)"
echo "  ${TAG_PREFIX}:latest                         - 最新版本别名"
echo ""
echo "使用示例："
echo "  # 运行rootless dind容器"
echo "  docker run --privileged --name ubuntu-dind-rootless -d ${TAG_PREFIX}:latest"
echo ""
echo "  # 连接到容器并使用Docker"
echo "  docker exec -it ubuntu-dind-rootless docker version"
echo ""
echo "  # 测试Docker功能"
echo "  docker exec ubuntu-dind-rootless docker run hello-world"

#!/bin/bash
set -e

# 构建基于Ubuntu 22.04的Docker镜像脚本

DOCKER_VERSION="28.2.1"
TAG_PREFIX="ubuntu-docker"

echo "构建基于Ubuntu 22.04的Docker镜像..."

# 构建CLI镜像
echo "构建Docker CLI镜像..."
docker build -f Dockerfile.ubuntu-cli -t "${TAG_PREFIX}:${DOCKER_VERSION}-cli" .

# 构建DIND镜像
echo "构建Docker-in-Docker镜像..."
docker build -f Dockerfile.ubuntu-dind -t "${TAG_PREFIX}:${DOCKER_VERSION}-dind" .

# 构建DIND Rootless镜像
echo "构建Docker-in-Docker Rootless镜像..."
docker build -f Dockerfile.ubuntu-dind-rootless -t "${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless" .

echo "构建完成！"
echo ""
echo "可用的镜像："
echo "  ${TAG_PREFIX}:${DOCKER_VERSION}-cli           - Docker CLI"
echo "  ${TAG_PREFIX}:${DOCKER_VERSION}-dind          - Docker-in-Docker"
echo "  ${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless - Docker-in-Docker Rootless (推荐)"
echo ""
echo "使用示例："
echo "  # 运行rootless dind容器"
echo "  docker run --privileged --name ubuntu-dind-rootless -d ${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless"
echo ""
echo "  # 连接到容器并使用Docker"
echo "  docker exec -it ubuntu-dind-rootless docker version"

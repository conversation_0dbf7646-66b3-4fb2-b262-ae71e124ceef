# 使用指南

## 快速开始

### 1. 构建镜像
```bash
./build-ubuntu-docker.sh
```

### 2. 运行容器
```bash
# 基本运行
docker run --privileged --name my-dind -d ubuntu-docker:latest

# 带数据持久化
docker run --privileged --name my-dind \
  -v dind-data:/home/<USER>/.local/share/docker \
  -d ubuntu-docker:latest

# 开发模式（挂载当前目录）
docker run --privileged --name my-dind-dev \
  -v dind-data:/home/<USER>/.local/share/docker \
  -v $(pwd):/workspace \
  -w /workspace \
  -d ubuntu-docker:latest
```

### 3. 使用Docker
```bash
# 检查Docker状态
docker exec my-dind docker version

# 运行测试容器
docker exec my-dind docker run hello-world

# 构建镜像
docker exec my-dind docker build -t test .

# 进入容器shell
docker exec -it my-dind bash
```

## Docker Compose 使用

### 启动服务
```bash
# 默认服务
docker-compose -f docker-compose.ubuntu.yml up -d

# 开发环境
docker-compose -f docker-compose.ubuntu.yml --profile dev up -d
```

### 管理服务
```bash
# 查看状态
docker-compose -f docker-compose.ubuntu.yml ps

# 查看日志
docker-compose -f docker-compose.ubuntu.yml logs ubuntu-dind-rootless

# 停止服务
docker-compose -f docker-compose.ubuntu.yml down
```

## 测试

运行完整测试套件：
```bash
./test-ubuntu-docker.sh
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   # 确保使用--privileged标志
   docker run --privileged ...
   ```

2. **容器无法启动**
   ```bash
   # 检查日志
   docker logs container-name
   ```

3. **Docker守护进程未启动**
   ```bash
   # 等待更长时间或手动启动
   docker exec container-name dockerd-entrypoint.sh dockerd &
   ```

### 调试命令

```bash
# 检查rootless状态
docker exec container-name id

# 检查Docker进程
docker exec container-name ps aux | grep docker

# 检查网络
docker exec container-name ip addr show

# 检查存储驱动
docker exec container-name docker info | grep "Storage Driver"
```

## 高级配置

### 自定义网络配置
```bash
docker run --privileged \
  -e DOCKERD_ROOTLESS_ROOTLESSKIT_NET=slirp4netns \
  -e DOCKERD_ROOTLESS_ROOTLESSKIT_MTU=1400 \
  ubuntu-docker:latest
```

### TLS配置
```bash
docker run --privileged \
  -p 2376:2376 \
  -e DOCKER_TLS_CERTDIR=/certs \
  -v certs-vol:/certs/client \
  ubuntu-docker:latest
```

### 性能优化
```bash
# 使用更快的存储驱动
docker run --privileged \
  --storage-opt dm.fs=ext4 \
  ubuntu-docker:latest
```

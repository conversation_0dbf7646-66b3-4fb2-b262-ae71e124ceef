# Docker-in-Docker on Ubuntu 22.04
FROM ubuntu:22.04 as cli-stage

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Docker CLI
RUN set -eux; \
    \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "$dpkgArch" in \
        'amd64') \
            url='https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'; \
            ;; \
        'arm64') \
            url='https://download.docker.com/linux/static/stable/aarch64/docker-28.2.1.tgz'; \
            ;; \
        'armhf') \
            url='https://download.docker.com/linux/static/stable/armhf/docker-28.2.1.tgz'; \
            ;; \
        *) echo >&2 "error: unsupported architecture ($dpkgArch)"; exit 1 ;; \
    esac; \
    \
    wget -O docker.tgz "$url"; \
    \
    tar --extract \
        --file docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner \
        'docker/docker' \
    ; \
    rm docker.tgz; \
    \
    docker --version

# 主要的dind镜像
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 从cli-stage复制docker CLI
COPY --from=cli-stage /usr/local/bin/docker /usr/local/bin/docker

# 安装Docker运行时依赖
# https://github.com/moby/moby/blob/0eecd59153c03ced5f5ddd79cc98f29e4d86daec/project/PACKAGERS.md#runtime-dependencies
RUN apt-get update && apt-get install -y \
    btrfs-progs \
    e2fsprogs \
    git \
    iptables \
    openssl \
    pigz \
    uidmap \
    xfsprogs \
    xz-utils \
    zfsutils-linux \
    wget \
    ca-certificates \
    iproute2 \
    kmod \
    && rm -rf /var/lib/apt/lists/*

# 设置iptables legacy支持
RUN set -eux; \
    update-alternatives --set iptables /usr/sbin/iptables-legacy; \
    update-alternatives --set ip6tables /usr/sbin/ip6tables-legacy; \
    update-alternatives --set arptables /usr/sbin/arptables-legacy; \
    update-alternatives --set ebtables /usr/sbin/ebtables-legacy

# 设置subuid/subgid以支持 "--userns-remap=default"
RUN set -eux; \
    addgroup --system dockremap; \
    adduser --system --ingroup dockremap dockremap; \
    echo 'dockremap:165536:65536' >> /etc/subuid; \
    echo 'dockremap:165536:65536' >> /etc/subgid

# 下载并安装完整的Docker二进制文件
RUN set -eux; \
    \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "$dpkgArch" in \
        'amd64') \
            url='https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'; \
            ;; \
        'arm64') \
            url='https://download.docker.com/linux/static/stable/aarch64/docker-28.2.1.tgz'; \
            ;; \
        'armhf') \
            url='https://download.docker.com/linux/static/stable/armhf/docker-28.2.1.tgz'; \
            ;; \
        *) echo >&2 "error: unsupported architecture ($dpkgArch)"; exit 1 ;; \
    esac; \
    \
    wget -O docker.tgz "$url"; \
    \
    tar --extract \
        --file docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner \
        --exclude 'docker/docker' \
    ; \
    rm docker.tgz; \
    \
    dockerd --version; \
    containerd --version; \
    ctr --version; \
    runc --version

# 下载dind脚本
# https://github.com/docker/docker/tree/master/hack/dind
ENV DIND_COMMIT=8d9e3502aba39127e4d12196dae16d306f76993d

RUN set -eux; \
    wget -O /usr/local/bin/dind "https://raw.githubusercontent.com/docker/docker/${DIND_COMMIT}/hack/dind"; \
    chmod +x /usr/local/bin/dind

# 复制入口脚本
COPY dockerd-entrypoint.sh /usr/local/bin/
COPY docker-entrypoint.sh /usr/local/bin/

# 创建必要的目录
RUN mkdir -p /var/lib/docker

VOLUME /var/lib/docker
EXPOSE 2375 2376

ENTRYPOINT ["dockerd-entrypoint.sh"]
CMD []

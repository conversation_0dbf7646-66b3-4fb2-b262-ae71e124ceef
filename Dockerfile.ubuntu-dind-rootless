# Docker-in-Docker Rootless on Ubuntu 22.04
FROM ubuntu:22.04 as cli-stage

# 设置环境变量避免交互式安装
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 安装基础依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    curl \
    gnupg \
    lsb-release \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 下载并安装Docker CLI (仅支持x86_64)
RUN set -eux; \
    wget -O docker.tgz 'https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'; \
    \
    tar --extract \
        --file docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner \
        'docker/docker' \
    ; \
    rm docker.tgz; \
    \
    docker --version

# 主要的dind-rootless镜像
FROM ubuntu:22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV DOCKER_VERSION=28.2.1

# 从cli-stage复制docker CLI
COPY --from=cli-stage /usr/local/bin/docker /usr/local/bin/docker

# 安装Docker运行时依赖和rootless相关工具
RUN apt-get update && apt-get install -y \
    btrfs-progs \
    e2fsprogs \
    git \
    iptables \
    openssl \
    pigz \
    uidmap \
    xfsprogs \
    xz-utils \
    zfsutils-linux \
    wget \
    ca-certificates \
    iproute2 \
    kmod \
    fuse-overlayfs \
    && rm -rf /var/lib/apt/lists/*

# 设置iptables legacy支持
RUN set -eux; \
    update-alternatives --set iptables /usr/sbin/iptables-legacy; \
    update-alternatives --set ip6tables /usr/sbin/ip6tables-legacy; \
    update-alternatives --set arptables /usr/sbin/arptables-legacy; \
    update-alternatives --set ebtables /usr/sbin/ebtables-legacy

# "/run/user/UID" 将被用作XDG_RUNTIME_DIR的默认值
RUN mkdir /run/user && chmod 1777 /run/user

# 创建预配置的rootless用户
RUN set -eux; \
    adduser --home /home/<USER>'Rootless' --disabled-password --uid 1000 rootless; \
    echo 'rootless:100000:65536' >> /etc/subuid; \
    echo 'rootless:100000:65536' >> /etc/subgid

# 下载并安装完整的Docker二进制文件 (仅支持x86_64)
RUN set -eux; \
    wget -O docker.tgz 'https://download.docker.com/linux/static/stable/x86_64/docker-28.2.1.tgz'; \
    \
    tar --extract \
        --file docker.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        --no-same-owner \
        --exclude 'docker/docker' \
    ; \
    rm docker.tgz; \
    \
    dockerd --version; \
    containerd --version; \
    ctr --version; \
    runc --version

# 下载并安装rootless extras (rootlesskit, vpnkit) - 仅支持x86_64
RUN set -eux; \
    wget -O 'rootless.tgz' 'https://download.docker.com/linux/static/stable/x86_64/docker-rootless-extras-28.2.1.tgz'; \
    \
    tar --extract \
        --file rootless.tgz \
        --strip-components 1 \
        --directory /usr/local/bin/ \
        'docker-rootless-extras/rootlesskit' \
        'docker-rootless-extras/vpnkit' \
    ; \
    rm rootless.tgz; \
    \
    rootlesskit --version; \
    vpnkit --version

# 下载dind脚本
ENV DIND_COMMIT=8d9e3502aba39127e4d12196dae16d306f76993d

RUN set -eux; \
    wget -O /usr/local/bin/dind "https://raw.githubusercontent.com/docker/docker/${DIND_COMMIT}/hack/dind"; \
    chmod +x /usr/local/bin/dind

# 为rootless用户预创建"/var/lib/docker"
RUN set -eux; \
    mkdir -p /home/<USER>/.local/share/docker; \
    chown -R rootless:rootless /home/<USER>/.local/share/docker

# 复制入口脚本
COPY dockerd-entrypoint.sh /usr/local/bin/
COPY docker-entrypoint.sh /usr/local/bin/

VOLUME /home/<USER>/.local/share/docker
EXPOSE 2375 2376

# 默认使用rootless用户
USER rootless

ENTRYPOINT ["dockerd-entrypoint.sh"]
CMD []

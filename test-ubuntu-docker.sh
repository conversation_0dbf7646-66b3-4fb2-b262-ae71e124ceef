#!/bin/bash
set -e

# 测试Ubuntu Docker镜像功能

DOCKER_VERSION="28.2.1"
TAG_PREFIX="ubuntu-docker"

echo "🧪 测试Ubuntu Docker镜像功能..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_image() {
    local image_name=$1
    local test_name=$2
    local container_name=$3
    
    echo -e "\n${YELLOW}📋 测试: $test_name${NC}"
    echo "镜像: $image_name"
    
    # 清理可能存在的容器
    docker rm -f "$container_name" 2>/dev/null || true
    
    # 启动容器
    echo "启动容器..."
    if [[ "$image_name" == *"rootless"* ]]; then
        docker run --privileged --name "$container_name" -d "$image_name"
    else
        docker run --privileged --name "$container_name" -d "$image_name"
    fi
    
    # 等待容器启动
    echo "等待Docker守护进程启动..."
    sleep 10
    
    # 测试Docker版本
    echo "测试Docker版本..."
    if docker exec "$container_name" docker version > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker版本检查通过${NC}"
        docker exec "$container_name" docker version --format "{{.Server.Version}}"
    else
        echo -e "${RED}❌ Docker版本检查失败${NC}"
        return 1
    fi
    
    # 测试运行容器
    echo "测试运行hello-world容器..."
    if docker exec "$container_name" docker run --rm hello-world > /dev/null 2>&1; then
        echo -e "${GREEN}✅ hello-world容器运行成功${NC}"
    else
        echo -e "${RED}❌ hello-world容器运行失败${NC}"
        return 1
    fi
    
    # 测试镜像构建
    echo "测试镜像构建..."
    if docker exec "$container_name" sh -c 'echo "FROM alpine:latest" | docker build -t test-build -' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 镜像构建测试通过${NC}"
    else
        echo -e "${RED}❌ 镜像构建测试失败${NC}"
        return 1
    fi
    
    # 测试Docker Compose（如果可用）
    echo "测试Docker Compose..."
    if docker exec "$container_name" docker compose version > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker Compose可用${NC}"
        docker exec "$container_name" docker compose version
    else
        echo -e "${YELLOW}⚠️  Docker Compose不可用或未安装${NC}"
    fi
    
    # 测试Docker Buildx
    echo "测试Docker Buildx..."
    if docker exec "$container_name" docker buildx version > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Docker Buildx可用${NC}"
        docker exec "$container_name" docker buildx version
    else
        echo -e "${YELLOW}⚠️  Docker Buildx不可用或未安装${NC}"
    fi
    
    # 显示系统信息
    echo "Docker系统信息:"
    docker exec "$container_name" docker info --format "Storage Driver: {{.Driver}}"
    docker exec "$container_name" docker info --format "Security Options: {{.SecurityOptions}}"
    
    # 清理测试容器
    echo "清理测试容器..."
    docker rm -f "$container_name"
    
    echo -e "${GREEN}✅ $test_name 测试完成${NC}"
}

# 检查镜像是否存在
check_image_exists() {
    local image_name=$1
    if ! docker image inspect "$image_name" > /dev/null 2>&1; then
        echo -e "${RED}❌ 镜像 $image_name 不存在，请先构建镜像${NC}"
        echo "运行: ./build-ubuntu-docker.sh"
        exit 1
    fi
}

# 主测试流程
main() {
    echo "检查镜像是否存在..."
    
    # 检查所有镜像
    check_image_exists "${TAG_PREFIX}:${DOCKER_VERSION}-cli"
    check_image_exists "${TAG_PREFIX}:${DOCKER_VERSION}-dind"
    check_image_exists "${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless"
    
    echo -e "${GREEN}✅ 所有镜像都存在${NC}"
    
    # 测试CLI镜像
    echo -e "\n${YELLOW}🔧 测试CLI镜像...${NC}"
    docker run --rm "${TAG_PREFIX}:${DOCKER_VERSION}-cli" docker --version
    echo -e "${GREEN}✅ CLI镜像测试通过${NC}"
    
    # 测试DIND镜像
    test_image "${TAG_PREFIX}:${DOCKER_VERSION}-dind" "Docker-in-Docker" "test-ubuntu-dind"
    
    # 测试DIND Rootless镜像
    test_image "${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless" "Docker-in-Docker Rootless" "test-ubuntu-dind-rootless"
    
    echo -e "\n${GREEN}🎉 所有测试完成！${NC}"
    echo "=================================="
    echo -e "${GREEN}✅ Ubuntu Docker镜像功能正常${NC}"
    echo ""
    echo "推荐使用rootless版本："
    echo "  docker run --privileged --name my-dind -d ${TAG_PREFIX}:${DOCKER_VERSION}-dind-rootless"
}

# 运行测试
main "$@"
